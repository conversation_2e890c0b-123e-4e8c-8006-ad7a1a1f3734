<template>
  <div>
    <a-tabs @change="handleChange">
      <a-tab-pane key="1" tab="监察人员">
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
          <!--插槽:table标题-->
          <template #tableTitle>
            <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
            <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
            <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleAudit">
                    审核
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown>
          </template>
          <!--操作栏-->
          <!-- :dropDownActions="getDropDownAction(record)" -->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" />
          </template>
        </BasicTable>
      </a-tab-pane>
      <a-tab-pane key="2" tab="监察计划" force-render>
        <BasicTable @register="registerTable2" :rowSelection="rowSelection2">
          <!--插槽:table标题-->
          <template #tableTitle>
            <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
            <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
            <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleAudit">
                    审核
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown>
          </template>
          <!--操作栏-->
          <!-- :actions="getTableAction(record)" -->
          <template #action="{ record }">
            <TableAction :dropDownActions="getDropDownAction(record)" />
          </template>
        </BasicTable>

      </a-tab-pane>
    </a-tabs>


    <!-- 表单区域 -->
    <annualSupervisionPlanModal @register="registerModal" @success="handleSuccess"></annualSupervisionPlanModal>
    <!-- 审批表打印 -->
    <annualSupervisionPlanTableModal @register="registerSPBModal" @success="handleSuccess">
    </annualSupervisionPlanTableModal>

    <!-- 驳回理由弹窗 -->
    <a-modal v-model:visible="rejectModalVisible" :title="rejectModalTitle" @ok="handleRejectConfirm"
      @cancel="handleRejectCancel" width="500px">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
          <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 上传证书弹窗 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传证书" @ok="handleUploadConfirm" @cancel="handleUploadCancel"
      width="500px">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="证书文件" :rules="[{ required: true, message: '请上传证书文件' }]">
          <j-upload v-model:value="certificateUrl" :maxCount="1" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 意见填写弹窗 -->
    <a-modal v-model:visible="commentModalVisible" :title="commentModalTitle" @ok="handleCommentConfirm"
      @cancel="handleCommentCancel" width="500px">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="意见内容">
          <a-textarea v-model:value="commentContent" placeholder="请输入意见内容（可选）" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 关键分析填写弹窗 -->
    <a-modal v-model:visible="keyAnalysisModalVisible" title="提交确认" @ok="handleKeyAnalysisConfirm"
      @cancel="handleKeyAnalysisCancel" width="500px">
      <a-form ref="keyAnalysisFormRef" :model="keyAnalysisForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="重点分析" name="keyAnalysis" :rules="[{ required: true, message: '请输入重点分析内容' }]">
          <a-textarea v-model:value="keyAnalysisForm.keyAnalysis" placeholder="请输入重点分析内容" :rows="4" :maxlength="500"
            show-count />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="userManagement-annualSupervisionPlan-index" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import annualSupervisionPlanModal from './modules/annualSupervisionPlanModal.vue';
import annualSupervisionPlanTableModal from './modules/annualSupervisionPlanTableModal.vue';
import { columns, searchFormSchema, columns2, searchFormSchema2 } from './annualSupervisionPlan.data';
import { list, list2, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './annualSupervisionPlan.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
import { def } from '@vue/shared';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerSPBModal, { openModal: openSPBModal }] = useModal();

// 驳回弹窗相关状态
const rejectModalVisible = ref(false);
const rejectModalTitle = ref('');
const rejectReason = ref('');
const currentRejectRecord = ref(null);
const rejectType = ref(''); // 'upAudit' 或 'audit'

// 上传证书弹窗相关状态
const uploadModalVisible = ref(false);
const certificateUrl = ref('');
const currentUploadRecord = ref(null);

// 意见填写弹窗相关状态
const commentModalVisible = ref(false);
const commentModalTitle = ref('');
const commentContent = ref('');
const currentCommentRecord = ref(null);
const commentType = ref(''); // 'audit' 或 'upAudit'

// 关键分析弹窗相关状态
const keyAnalysisModalVisible = ref(false);
const currentConfirmRecord = ref<any>(null);
const keyAnalysisFormRef = ref();

// 关键分析表单数据
const keyAnalysisForm = ref({
  keyAnalysis: ''
});
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '监察人员',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
});
const { tableContext: tableContext2 } = useListPage({
  tableProps: {
    title: '年度监察计划',
    api: list2,
    columns: columns2,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema2,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '年度监察计划',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload, getSelectRows }, { rowSelection, selectedRowKeys }] = tableContext;
const [registerTable2, { reload: reload2, getSelectRows: getSelectRows2 }, { rowSelection: rowSelection2 }] = tableContext2;
function handleChange(e) {
  if (e == 1) {
    reload();
  } else {
    reload2()
  }
}
/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 审核
 */
function batchHandleAudit() {
  defHttp.post({
    url: '/lims/employee/monitorPlanAuditAdd',
    params: getSelectRows(),
  })
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record: [{ ...record }],
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await defHttp.get({ url: '/lims/employee/monitorPlanAuditDelete?id=' + record.id });
  reload2()
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload2);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  openSPBModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}

/**
 *  技术审核
 */
function handleAudit(record) {
  currentCommentRecord.value = record;
  commentType.value = 'audit';
  commentModalTitle.value = '审核意见';
  commentContent.value = '';
  commentModalVisible.value = true;
}
/**
 * 技术审核驳回
 */
function handleAuditReject(record) {
  currentRejectRecord.value = record;
  rejectType.value = 'audit';
  rejectModalTitle.value = '技术审核驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 驳回确认
 */
async function handleRejectConfirm() {
  if (!rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  try {
    const params: any = {
      id: currentRejectRecord.value.id,
    };
    // 根据驳回类型设置不同的理由字段
    if (rejectType.value === 'upAudit') {
      params.auditContent = rejectReason.value;
      params.auditStatus = 99;
    } else if (rejectType.value === 'audit') {
      params.rejectReason = rejectReason.value;
      params.auditStatus = 99;
    }
    await defHttp.post({
      url: '/lims/employee/monitorPlanAuditOrRollBack',
      params,
    });
    rejectModalVisible.value = false;
    reload2();
  } catch (error) {
  }
}

/**
 * 驳回取消
 */
function handleRejectCancel() {
  rejectModalVisible.value = false;
  rejectReason.value = '';
  currentRejectRecord.value = null;
}

/**
 * 上传证书
 */
function handleUpload(record) {
  currentUploadRecord.value = record;
  certificateUrl.value = '';
  uploadModalVisible.value = true;
}

/**
 * 上传证书确认
 */
async function handleUploadConfirm() {
  if (!certificateUrl.value) {
    message.error('请上传证书文件');
    return;
  }

  try {
    await defHttp.put({
      url: '/lims/employee/uploadOutTrainingData',
      data: {
        id: currentUploadRecord.value.id,
        certificateUrl: certificateUrl.value,
      },
    });

    message.success('证书上传成功');
    uploadModalVisible.value = false;
    reload2();
  } catch (error) {
    message.error('证书上传失败');
  }
}

/**
 * 上传证书取消
 */
function handleUploadCancel() {
  uploadModalVisible.value = false;
  certificateUrl.value = '';
  currentUploadRecord.value = null;
}

/**
 * 意见确认
 */
async function handleCommentConfirm() {
  try {
    const params: any = {
      id: currentCommentRecord.value.id,
    };

    if (commentType.value === 'audit') {
      // 技术审核
      params.auditContent = commentContent.value || '';
      params.auditStatus = 1;
    } else if (commentType.value === 'upAudit') {
      // 同意
      params.assignContent = commentContent.value || '';
      params.auditStatus = 2;
    }

    await defHttp.post({
      url: '/lims/employee/monitorPlanAuditOrRollBack',
      params,
    });

    commentModalVisible.value = false;
    reload2();
  } catch (error) {
  }
}
/**
 * 提交 - 显示关键分析填写弹窗
 */
function handleConfirm(record) {
  currentConfirmRecord.value = record;
  keyAnalysisForm.value.keyAnalysis = '';
  keyAnalysisModalVisible.value = true;
}

/**
 * 关键分析确认提交
 */
async function handleKeyAnalysisConfirm() {
  try {
    // 表单验证
    await keyAnalysisFormRef.value.validate();

    if (!currentConfirmRecord.value) {
      message.error('记录信息丢失，请重新操作');
      return;
    }

    await defHttp.post({
      url: '/lims/employee/monitorPlanCommit',
      params: {
        id: currentConfirmRecord.value.id,
        keyAnalysis: keyAnalysisForm.value.keyAnalysis
      },
    });

    message.success('提交成功');
    keyAnalysisModalVisible.value = false;
    reload2();
  } catch (error) {
    console.error('提交失败:', error);
  }
}

/**
 * 关键分析取消
 */
function handleKeyAnalysisCancel() {
  keyAnalysisModalVisible.value = false;
  keyAnalysisForm.value.keyAnalysis = '';
  currentConfirmRecord.value = null;
}

/**
 * 意见取消
 */
function handleCommentCancel() {
  commentModalVisible.value = false;
  commentContent.value = '';
  currentCommentRecord.value = null;
}

/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
  reload2();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      // ifShow: () => {
      //   return record.auditStatus == '0';
      // },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '提交',
      onClick: handleConfirm.bind(null, record),
      ifShow: () => {
        return record.auditStatus == null || record.auditStatus == '' || record.auditStatus == 99;
      },
    },
    {
      label: '审核',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '驳回',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAuditReject.bind(null, record),
    },
    {
      label: '年度监督计划表',
      onClick: handleSeeSPB.bind(null, record),
    },

  ];
}
</script>
<style scoped></style>
